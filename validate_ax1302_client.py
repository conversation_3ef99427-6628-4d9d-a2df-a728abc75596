#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import base64
import requests
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

class AX1302Client:
    """AX1302 协议登录客户端"""
    
    def __init__(self, base_url="https://www.ax1302.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.public_key = None
        self.device_id = int(time.time() * 1000)
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
        })
    
    def initialize_encryption(self):
        """初始化加密模块"""
        try:
            response = self.session.get(f"{self.base_url}/web/encrypt/key")
            response.raise_for_status()

            data = response.json()
            if data.get('success'):
                # 获取公钥字符串，这是 Base64 编码的 DER 格式
                public_key_str = data['items']
                print(f"获取到公钥: {public_key_str[:50]}...")

                try:
                    # 方法1: 直接解码 Base64 并加载 DER 格式
                    public_key_bytes = base64.b64decode(public_key_str)
                    self.public_key = serialization.load_der_public_key(
                        public_key_bytes,
                        backend=default_backend()
                    )
                    print("✓ 使用 DER 格式加载公钥成功")
                    return True

                except Exception as der_error:
                    print(f"DER 格式加载失败: {der_error}")

                    try:
                        # 方法2: 构造 PEM 格式
                        pem_header = "-----BEGIN PUBLIC KEY-----\n"
                        pem_footer = "\n-----END PUBLIC KEY-----"

                        # 将 Base64 字符串按 64 字符分行
                        formatted_key = ""
                        for i in range(0, len(public_key_str), 64):
                            formatted_key += public_key_str[i:i+64] + "\n"

                        pem_key = pem_header + formatted_key.rstrip() + pem_footer
                        print(f"构造的 PEM 格式: {pem_key[:100]}...")

                        # 加载 PEM 格式的公钥
                        self.public_key = serialization.load_pem_public_key(
                            pem_key.encode('utf-8'),
                            backend=default_backend()
                        )
                        print("✓ 使用 PEM 格式加载公钥成功")
                        return True

                    except Exception as pem_error:
                        print(f"PEM 格式加载也失败: {pem_error}")
                        return False

            print("服务器响应格式错误")
            return False

        except Exception as e:
            print(f"网络请求失败: {e}")
            return False
    
    def encrypt_data(self, plaintext):
        """数据加密处理"""
        if not self.public_key:
            raise ValueError("加密模块未初始化")
        
        try:
            # 检查数据长度，RSA 2048位密钥最大加密长度为 245 字节
            max_length = 245
            if len(plaintext.encode('utf-8')) > max_length:
                raise ValueError(f"数据长度超过限制，最大 {max_length} 字节")
            
            # 使用 PKCS1v15 填充进行 RSA 加密
            ciphertext = self.public_key.encrypt(
                plaintext.encode('utf-8'),
                padding.PKCS1v15()
            )
            
            # 返回 Base64 编码的密文
            return base64.b64encode(ciphertext).decode('utf-8')
            
        except Exception as e:
            print(f"加密失败: {e}")
            return None
    
    def get_verification_code(self, save_path="verification.png"):
        """获取验证码"""
        try:
            captcha_url = f"{self.base_url}/web/captcha?deviceID={self.device_id}"
            response = self.session.get(captcha_url)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        except Exception as e:
            print(f"获取验证码失败: {e}")
            return False
    
    def authenticate(self, username, password, verification_code):
        """执行身份验证"""
        try:
            if not self.public_key:
                if not self.initialize_encryption():
                    return {"success": False, "error": "加密初始化失败"}
            
            # 用户名预处理
            if not username.startswith('x11'):
                username = 'x11' + username
            
            # 数据加密
            encrypted_username = self.encrypt_data(username)
            encrypted_password = self.encrypt_data(password)
            
            if not encrypted_username or not encrypted_password:
                return {"success": False, "error": "数据加密失败"}
            
            # 构建请求数据
            auth_data = {
                "username": encrypted_username,
                "password": encrypted_password,
                "captcha": verification_code,
                "deviceID": self.device_id
            }
            
            # 发送认证请求
            headers = {
                'Content-Type': 'application/json',
                'encrypt': '1'
            }
            
            response = self.session.post(
                f"{self.base_url}/web/login?ga=1",
                headers=headers,
                json={"data": auth_data}
            )
            
            # 处理响应
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_headers": dict(response.headers),
                "response_content": response.text
            }
            
            if response.history:
                result["redirected"] = True
                result["final_url"] = response.url
            
            try:
                if response.text:
                    result["json_response"] = response.json()
            except:
                pass
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}

def validate_client():
    """验证客户端实现"""
    print("开始验证 AX1302 客户端实现...")
    
    try:
        # 1. 创建客户端实例
        client = AX1302Client()
        print("✓ 客户端创建成功")
        
        # 2. 测试加密模块初始化
        if client.initialize_encryption():
            print("✓ 加密模块初始化成功")
            print(f"  公钥类型: {type(client.public_key)}")
        else:
            print("✗ 加密模块初始化失败")
            return False
        
        # 3. 测试数据加密
        test_data = "x11testuser"
        encrypted = client.encrypt_data(test_data)
        
        if encrypted:
            print("✓ 数据加密成功")
            print(f"  原文长度: {len(test_data)} 字符")
            print(f"  密文长度: {len(encrypted)} 字符")
            print(f"  密文示例: {encrypted[:50]}...")
        else:
            print("✗ 数据加密失败")
            return False
        
        # 4. 测试验证码获取
        captcha_file = "test_captcha.png"
        if client.get_verification_code(captcha_file):
            print("✓ 验证码获取成功")
            print(f"  验证码文件: {captcha_file}")
            
            # 检查文件是否存在
            import os
            if os.path.exists(captcha_file):
                file_size = os.path.getsize(captcha_file)
                print(f"  文件大小: {file_size} 字节")
            
            # 清理测试文件
            try:
                os.remove(captcha_file)
            except:
                pass
        else:
            print("✗ 验证码获取失败")
            return False
        
        # 5. 测试请求构造（不实际发送）
        test_auth_data = {
            "username": encrypted,
            "password": client.encrypt_data("testpassword"),
            "captcha": "1234",
            "deviceID": client.device_id
        }
        
        print("✓ 认证数据构造成功")
        print(f"  设备ID: {client.device_id}")
        print(f"  数据结构完整性: {all(test_auth_data.values())}")
        
        print("\n验证结果: 所有核心功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 验证过程出现异常: {e}")
        return False

def test_actual_login():
    """测试实际登录流程"""
    print("\n开始实际登录测试...")
    
    client = AX1302Client()
    
    # 初始化加密
    if not client.initialize_encryption():
        print("✗ 加密初始化失败")
        return False
    
    # 获取验证码
    if not client.get_verification_code("login_captcha.png"):
        print("✗ 验证码获取失败")
        return False
    
    print("✓ 验证码已保存到 login_captcha.png")
    
    # 测试登录（使用测试数据）
    result = client.authenticate("testuser", "testpass", "1234")
    
    print(f"登录测试结果:")
    print(f"  成功: {result['success']}")
    print(f"  状态码: {result.get('status_code')}")
    print(f"  响应内容: {result.get('response_content', '')[:100]}...")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("AX1302 客户端验证测试")
    print("=" * 60)
    
    # 基础功能验证
    if validate_client():
        print("\n基础功能验证通过")
        
        # 实际登录测试
        test_actual_login()
    else:
        print("\n基础功能验证失败")
    
    print("\n验证完成")
