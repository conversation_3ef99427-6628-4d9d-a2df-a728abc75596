# Office-PowerPoint-MCP-Server 使用指南

## 项目概述

Office-PowerPoint-MCP-Server 是一个功能强大的 MCP (Model Context Protocol) 服务器，专门用于 PowerPoint 文件的操作和管理。该项目提供了 32 个专业工具，支持完整的 PowerPoint 创建、编辑和管理功能。

## 🚀 安装状态

✅ **安装成功！** 
- 版本：2.0.6
- 安装方式：PyPI 安装
- 配置方式：UVX（推荐）

## 📁 项目结构

```
Office-PowerPoint-MCP-Server/
├── ppt_mcp_server.py          # 主服务器文件
├── mcp-config.json           # MCP 配置文件（已生成）
├── templates/                # PowerPoint 模板目录
│   ├── sample_template.pptx  # 示例模板（已创建）
│   └── README.md            # 模板使用说明
├── tools/                   # 32个专业工具模块
├── utils/                   # 工具函数库
└── setup_mcp.py            # 自动安装脚本
```

## ⚙️ Augment Code 配置

将以下配置添加到你的 Augment Code MCP 配置中：

```json
{
  "mcpServers": {
    "ppt": {
      "command": "uvx",
      "args": [
        "--from",
        "office-powerpoint-mcp-server",
        "ppt_mcp_server"
      ],
      "env": {
        "PPT_TEMPLATE_PATH": "D:\\CLT\\MCP\\Office-PowerPoint-MCP-Server\\templates"
      }
    }
  }
}
```

## 🔧 核心功能

### 1. 演示文稿管理（7个工具）
- `create_presentation` - 创建新演示文稿
- `create_presentation_from_template` - 从模板创建演示文稿
- `open_presentation` - 打开现有演示文稿
- `save_presentation` - 保存演示文稿
- `get_presentation_info` - 获取演示文稿信息
- `get_template_file_info` - 分析模板文件
- `set_core_properties` - 设置文档属性

### 2. 内容管理（8个工具）
- `add_slide` - 添加幻灯片
- `get_slide_info` - 获取幻灯片信息
- `extract_slide_text` - 提取幻灯片文本
- `extract_presentation_text` - 提取整个演示文稿文本
- `populate_placeholder` - 填充占位符
- `add_bullet_points` - 添加项目符号
- `manage_text` - 统一文本管理工具
- `manage_image` - 统一图像管理工具

### 3. 模板操作（7个工具）
- `list_slide_templates` - 浏览可用模板
- `apply_slide_template` - 应用模板到现有幻灯片
- `create_slide_from_template` - 从模板创建新幻灯片
- `create_presentation_from_templates` - 从模板序列创建演示文稿
- `get_template_info` - 获取模板详细信息
- `auto_generate_presentation` - 自动生成演示文稿
- `optimize_slide_text` - 优化幻灯片文本

### 4. 结构元素（4个工具）
- `add_table` - 创建表格
- `format_table_cell` - 格式化表格单元格
- `add_shape` - 添加形状
- `add_chart` - 创建图表

### 5. 专业设计（3个工具）
- `apply_professional_design` - 应用专业设计主题
- `apply_picture_effects` - 应用图片效果
- `manage_fonts` - 字体管理

## 🎨 内置功能特色

### 专业色彩方案
- **Modern Blue** - 现代蓝色主题
- **Corporate Gray** - 企业灰色主题  
- **Elegant Green** - 优雅绿色主题
- **Warm Red** - 温暖红色主题

### 25+ 内置幻灯片模板
- 标题和介绍幻灯片
- 内容布局幻灯片
- 商业和分析幻灯片
- 流程和流程图幻灯片
- 团队和组织幻灯片

## 🚀 快速开始

### 1. 基本使用示例
```python
# 创建新演示文稿
result = create_presentation()
presentation_id = result["presentation_id"]

# 添加标题幻灯片
result = add_slide(
    layout_index=0,
    title="我的演示文稿",
    presentation_id=presentation_id
)

# 保存演示文稿
result = save_presentation(
    file_path="my_presentation.pptx",
    presentation_id=presentation_id
)
```

### 2. 使用模板创建演示文稿
```python
# 从模板创建演示文稿
result = create_presentation_from_template("sample_template.pptx")

# 应用专业设计
result = apply_professional_design(
    operation="theme",
    color_scheme="modern_blue"
)
```

## 📝 模板使用

### 模板目录
- 默认模板路径：`./templates/`
- 支持格式：`.pptx` 和 `.potx` 文件
- 已创建示例模板：`sample_template.pptx`

### 自定义模板路径
通过环境变量 `PPT_TEMPLATE_PATH` 设置自定义模板目录：
```bash
# Windows
set PPT_TEMPLATE_PATH="C:\templates;C:\company_templates"

# Unix/Linux/macOS  
export PPT_TEMPLATE_PATH="/path/to/templates:/another/path"
```

## 🔍 测试功能

你可以使用以下命令测试模板功能：
- `get_template_info('sample_template.pptx')`
- `create_presentation_from_template('sample_template.pptx')`

## 📚 更多资源

- 完整 API 文档：查看 `README.md`
- 模板使用指南：查看 `templates/README.md`
- 项目主页：https://github.com/GongRzhe/Office-PowerPoint-MCP-Server

## ✅ 下一步

1. 将上述 MCP 配置添加到 Augment Code
2. 重启 Augment Code 以加载 MCP 服务器
3. 开始使用 PowerPoint 操作功能
4. 根据需要添加自定义模板到 `templates/` 目录

---

**安装完成时间：** 2025-09-05  
**配置状态：** ✅ 就绪  
**模板状态：** ✅ 示例模板已创建
