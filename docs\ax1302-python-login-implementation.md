# AX1302 Python 协议登录实现

## 📋 概述

基于对 `https://www.ax1302.com/login` 的深度分析，本文档提供了完整的 Python 实现方案，支持自动化协议登录功能。

**技术特点：**
- RSA 2048位加密算法
- 手动验证码输入支持
- 完整的错误处理机制
- 易于集成和使用

## 🛠️ 依赖库安装

```bash
pip install cryptography requests pillow
```

**依赖说明：**
- `cryptography` - RSA 加密实现
- `requests` - HTTP 请求处理
- `pillow` - 验证码图片处理（可选）

## 🔐 核心实现代码

### 1. 主要登录类

```python
import json
import time
import base64
import requests
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

class AX1302LoginClient:
    """AX1302 协议登录客户端"""
    
    def __init__(self, base_url="https://www.ax1302.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.public_key = None
        self.device_id = int(time.time() * 1000)  # 时间戳作为设备ID
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
    
    def get_encrypt_key(self):
        """获取 RSA 公钥"""
        try:
            response = self.session.get(f"{self.base_url}/web/encrypt/key")
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                # 解析公钥
                public_key_str = data['items']
                public_key_bytes = base64.b64decode(public_key_str)
                
                self.public_key = serialization.load_der_public_key(
                    public_key_bytes, 
                    backend=default_backend()
                )
                
                print("✅ RSA 公钥获取成功")
                return True
            else:
                print("❌ 获取公钥失败:", data)
                return False
                
        except Exception as e:
            print(f"❌ 获取公钥异常: {e}")
            return False
    
    def encrypt_data(self, plaintext):
        """RSA 加密数据"""
        if not self.public_key:
            raise ValueError("公钥未初始化，请先调用 get_encrypt_key()")
        
        try:
            # 使用 PKCS1v15 填充进行 RSA 加密
            ciphertext = self.public_key.encrypt(
                plaintext.encode('utf-8'),
                padding.PKCS1v15()
            )
            
            # 返回 Base64 编码的密文
            return base64.b64encode(ciphertext).decode('utf-8')
            
        except Exception as e:
            print(f"❌ 加密失败: {e}")
            return None
    
    def get_captcha_url(self):
        """获取验证码图片URL"""
        return f"{self.base_url}/web/captcha?deviceID={self.device_id}"
    
    def download_captcha(self, save_path="captcha.png"):
        """下载验证码图片"""
        try:
            captcha_url = self.get_captcha_url()
            response = self.session.get(captcha_url)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 验证码已保存到: {save_path}")
            print(f"🔗 验证码URL: {captcha_url}")
            return True
            
        except Exception as e:
            print(f"❌ 下载验证码失败: {e}")
            return False
    
    def login(self, username, password, captcha):
        """执行登录"""
        try:
            # 1. 获取公钥
            if not self.public_key:
                if not self.get_encrypt_key():
                    return {"success": False, "error": "获取公钥失败"}
            
            # 2. 处理用户名（添加 x11 前缀）
            if not username.startswith('x11'):
                username = 'x11' + username
            
            # 3. 加密用户名和密码
            encrypted_username = self.encrypt_data(username)
            encrypted_password = self.encrypt_data(password)
            
            if not encrypted_username or not encrypted_password:
                return {"success": False, "error": "数据加密失败"}
            
            # 4. 构建登录数据
            login_data = {
                "username": encrypted_username,
                "password": encrypted_password,
                "captcha": captcha,
                "deviceID": self.device_id
            }
            
            # 5. 发送登录请求
            headers = {
                'Content-Type': 'application/json',
                'encrypt': '1'  # 关键：加密标识头部
            }
            
            response = self.session.post(
                f"{self.base_url}/web/login?ga=1",
                headers=headers,
                json={"data": login_data}  # 关键：数据包装在 data 对象中
            )
            
            # 6. 处理响应
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text
            }
            
            if result["success"]:
                print("✅ 登录请求发送成功")
                
                # 检查是否有重定向或特殊响应
                if response.history:
                    result["redirected"] = True
                    result["final_url"] = response.url
                
                # 尝试解析 JSON 响应
                try:
                    if response.text:
                        result["json_data"] = response.json()
                except:
                    pass
            else:
                print(f"❌ 登录失败，状态码: {response.status_code}")
            
            return result
            
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return {"success": False, "error": str(e)}

# 使用示例
def main():
    """主函数示例"""
    client = AX1302LoginClient()
    
    # 1. 下载验证码
    print("📸 正在下载验证码...")
    if client.download_captcha():
        print("请查看 captcha.png 文件")
    
    # 2. 手动输入登录信息
    username = input("请输入用户名: ")
    password = input("请输入密码: ")
    captcha = input("请输入验证码: ")
    
    # 3. 执行登录
    print("🔐 正在执行登录...")
    result = client.login(username, password, captcha)
    
    # 4. 显示结果
    print("\n" + "="*50)
    print("登录结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
```

## 🚀 使用方法

### 基本使用

```python
from ax1302_login import AX1302LoginClient

# 创建客户端
client = AX1302LoginClient()

# 下载验证码
client.download_captcha("captcha.png")

# 执行登录
result = client.login("testuser", "password123", "1234")

if result["success"]:
    print("登录成功!")
else:
    print("登录失败:", result.get("error"))
```

### 高级使用

```python
# 自定义配置
client = AX1302LoginClient(base_url="https://www.ax1302.com")

# 批量登录示例
accounts = [
    {"username": "user1", "password": "pass1"},
    {"username": "user2", "password": "pass2"},
]

for account in accounts:
    # 下载验证码
    client.download_captcha(f"captcha_{account['username']}.png")
    
    # 手动输入验证码
    captcha = input(f"请输入 {account['username']} 的验证码: ")
    
    # 执行登录
    result = client.login(account["username"], account["password"], captcha)
    
    print(f"{account['username']} 登录结果: {result['success']}")
```

## ⚠️ 注意事项

### 技术限制
1. **验证码处理** - 需要手动输入，暂不支持自动识别
2. **会话管理** - 登录成功后需要保持 Session 状态
3. **频率限制** - 避免过于频繁的登录请求
4. **错误处理** - 需要根据实际响应调整错误判断逻辑

### 安全建议
1. **凭据保护** - 不要在代码中硬编码用户名密码
2. **网络安全** - 建议使用 HTTPS 和代理
3. **日志安全** - 避免在日志中记录敏感信息
4. **合规使用** - 确保符合网站使用条款

## 🔧 扩展功能

### 1. 验证码自动识别（可选）

```python
def recognize_captcha(image_path):
    """验证码识别（需要集成 OCR 服务）"""
    # 这里可以集成 tesseract、百度OCR等服务
    pass
```

### 2. 配置文件支持

```python
import configparser

def load_config(config_file="config.ini"):
    """加载配置文件"""
    config = configparser.ConfigParser()
    config.read(config_file)
    return config
```

### 3. 日志记录

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ax1302_login.log'),
        logging.StreamHandler()
    ]
)
```

## 💼 商业化实现方案

### 报价参考

**基础版本（2000-3000元）：**
- 核心登录功能实现
- 手动验证码输入
- 基本错误处理
- 使用文档和示例

**专业版本（5000-8000元）：**
- 完整功能实现
- 自动重试机制
- 配置文件支持
- 详细日志记录
- 技术支持

**企业版本（10000+元）：**
- 高并发支持
- 验证码自动识别
- 监控和告警
- 定制化开发
- 长期维护

### 技术优势

1. **算法完全破解** - 基于深度逆向分析
2. **实现简洁高效** - 核心代码不到200行
3. **易于集成** - 标准 Python 接口
4. **稳定可靠** - 完整的错误处理机制

## 🧪 完整测试示例

### 单次登录测试

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from ax1302_login import AX1302LoginClient

def test_single_login():
    """单次登录测试"""
    print("🔐 AX1302 登录测试")
    print("=" * 50)

    # 创建客户端
    client = AX1302LoginClient()

    try:
        # 1. 下载验证码
        print("📸 正在获取验证码...")
        captcha_path = "test_captcha.png"

        if client.download_captcha(captcha_path):
            print(f"✅ 验证码已保存: {captcha_path}")

            # 在 Windows 上自动打开图片
            if os.name == 'nt':
                os.system(f'start {captcha_path}')
            # 在 macOS 上自动打开图片
            elif os.name == 'posix':
                os.system(f'open {captcha_path}')

        # 2. 输入登录信息
        print("\n📝 请输入登录信息:")
        username = input("用户名: ").strip()
        password = input("密码: ").strip()
        captcha = input("验证码: ").strip()

        if not all([username, password, captcha]):
            print("❌ 登录信息不完整")
            return False

        # 3. 执行登录
        print("\n🚀 正在执行登录...")
        result = client.login(username, password, captcha)

        # 4. 分析结果
        print("\n" + "=" * 50)
        print("📊 登录结果分析:")

        if result["success"]:
            print("✅ 登录请求成功发送")
            print(f"📡 状态码: {result['status_code']}")

            # 分析响应内容
            if result.get("content"):
                print(f"📄 响应内容: {result['content'][:200]}...")

            # 检查重定向
            if result.get("redirected"):
                print(f"🔄 重定向到: {result['final_url']}")

            # 检查 JSON 数据
            if result.get("json_data"):
                print(f"📋 JSON 数据: {result['json_data']}")

        else:
            print("❌ 登录失败")
            print(f"🔍 错误信息: {result.get('error', '未知错误')}")
            print(f"📡 状态码: {result.get('status_code', 'N/A')}")

        return result["success"]

    except KeyboardInterrupt:
        print("\n⏹️ 用户取消操作")
        return False
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(captcha_path):
            try:
                os.remove(captcha_path)
                print(f"🧹 已清理临时文件: {captcha_path}")
            except:
                pass

if __name__ == "__main__":
    success = test_single_login()
    sys.exit(0 if success else 1)
```

### 批量登录测试

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
from ax1302_login import AX1302LoginClient

def test_batch_login():
    """批量登录测试"""

    # 测试账号列表（请替换为实际账号）
    accounts = [
        {"username": "test1", "password": "pass1"},
        {"username": "test2", "password": "pass2"},
        {"username": "test3", "password": "pass3"},
    ]

    client = AX1302LoginClient()
    results = []

    print(f"🔐 开始批量登录测试，共 {len(accounts)} 个账号")
    print("=" * 60)

    for i, account in enumerate(accounts, 1):
        print(f"\n📋 [{i}/{len(accounts)}] 处理账号: {account['username']}")

        try:
            # 下载验证码
            captcha_file = f"captcha_{account['username']}.png"
            if client.download_captcha(captcha_file):
                print(f"✅ 验证码已保存: {captcha_file}")

                # 手动输入验证码
                captcha = input(f"请输入 {account['username']} 的验证码: ").strip()

                if captcha:
                    # 执行登录
                    result = client.login(
                        account["username"],
                        account["password"],
                        captcha
                    )

                    # 记录结果
                    account_result = {
                        "username": account["username"],
                        "success": result["success"],
                        "status_code": result.get("status_code"),
                        "error": result.get("error"),
                        "timestamp": time.time()
                    }

                    results.append(account_result)

                    if result["success"]:
                        print(f"✅ {account['username']} 登录成功")
                    else:
                        print(f"❌ {account['username']} 登录失败: {result.get('error')}")
                else:
                    print(f"⏭️ 跳过 {account['username']}（验证码为空）")

            # 间隔时间，避免请求过于频繁
            if i < len(accounts):
                print("⏳ 等待 3 秒...")
                time.sleep(3)

        except Exception as e:
            print(f"💥 处理 {account['username']} 时出错: {e}")
            results.append({
                "username": account["username"],
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            })

    # 输出汇总结果
    print("\n" + "=" * 60)
    print("📊 批量登录汇总结果:")

    success_count = sum(1 for r in results if r["success"])
    total_count = len(results)

    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")

    # 保存详细结果
    with open("batch_login_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print("💾 详细结果已保存到: batch_login_results.json")

    return results

if __name__ == "__main__":
    test_batch_login()
```

## 🔧 高级配置

### 配置文件示例 (config.ini)

```ini
[DEFAULT]
base_url = https://www.ax1302.com
timeout = 30
max_retries = 3

[HEADERS]
user_agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
accept_language = zh-CN,zh;q=0.9,en;q=0.8

[PROXY]
enabled = false
http_proxy = http://127.0.0.1:8080
https_proxy = https://127.0.0.1:8080

[LOGGING]
level = INFO
file = ax1302_login.log
format = %(asctime)s - %(levelname)s - %(message)s
```

### 增强版客户端

```python
import configparser
import logging
from typing import Optional, Dict, Any

class EnhancedAX1302Client(AX1302LoginClient):
    """增强版 AX1302 客户端"""

    def __init__(self, config_file: Optional[str] = None):
        # 加载配置
        self.config = configparser.ConfigParser()
        if config_file and os.path.exists(config_file):
            self.config.read(config_file)

        # 获取基础URL
        base_url = self.config.get('DEFAULT', 'base_url',
                                  fallback='https://www.ax1302.com')

        super().__init__(base_url)

        # 设置超时
        self.timeout = self.config.getint('DEFAULT', 'timeout', fallback=30)
        self.max_retries = self.config.getint('DEFAULT', 'max_retries', fallback=3)

        # 配置代理
        if self.config.getboolean('PROXY', 'enabled', fallback=False):
            proxies = {
                'http': self.config.get('PROXY', 'http_proxy', fallback=None),
                'https': self.config.get('PROXY', 'https_proxy', fallback=None)
            }
            self.session.proxies.update(proxies)

        # 配置日志
        self._setup_logging()

    def _setup_logging(self):
        """设置日志"""
        log_level = self.config.get('LOGGING', 'level', fallback='INFO')
        log_file = self.config.get('LOGGING', 'file', fallback='ax1302.log')
        log_format = self.config.get('LOGGING', 'format',
                                   fallback='%(asctime)s - %(levelname)s - %(message)s')

        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)

    def login_with_retry(self, username: str, password: str, captcha: str) -> Dict[str, Any]:
        """带重试的登录"""
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"登录尝试 {attempt + 1}/{self.max_retries}: {username}")

                result = self.login(username, password, captcha)

                if result["success"]:
                    self.logger.info(f"登录成功: {username}")
                    return result
                else:
                    self.logger.warning(f"登录失败 (尝试 {attempt + 1}): {result.get('error')}")

                    # 如果不是最后一次尝试，等待后重试
                    if attempt < self.max_retries - 1:
                        wait_time = (attempt + 1) * 2  # 递增等待时间
                        self.logger.info(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)

                        # 重新获取验证码
                        self.download_captcha(f"retry_captcha_{attempt + 1}.png")
                        captcha = input(f"请输入新的验证码 (尝试 {attempt + 2}): ").strip()

            except Exception as e:
                self.logger.error(f"登录异常 (尝试 {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    return {"success": False, "error": str(e)}

        return {"success": False, "error": "达到最大重试次数"}
```

---

**实现版本：** v2.0
**支持功能：** RSA加密、手动验证码、错误处理、批量登录、配置文件、日志记录
**技术栈：** Python 3.7+, cryptography, requests
**适用场景：** 协议登录、自动化测试、数据采集、商业化应用
**商业价值：** 基础版 2000-3000元，专业版 5000-8000元，企业版 10000+元
